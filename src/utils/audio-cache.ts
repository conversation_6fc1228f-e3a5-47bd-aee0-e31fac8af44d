/**
 * Audio cache utility for preloading and managing audio files
 */

class AudioCache {
  private cache: Map<string, HTMLAudioElement> = new Map();
  private loadingPromises: Map<string, Promise<HTMLAudioElement>> = new Map();

  /**
   * Preload an audio file and store it in cache
   */
  async preload(soundFile: string): Promise<HTMLAudioElement> {
    const url = `https://static.bestonlineclock.com/sounds/${soundFile}`;
    
    // If already cached, return the cached audio
    if (this.cache.has(soundFile)) {
      return this.cache.get(soundFile)!;
    }

    // If already loading, return the existing promise
    if (this.loadingPromises.has(soundFile)) {
      return this.loadingPromises.get(soundFile)!;
    }

    // Create new loading promise
    const loadingPromise = new Promise<HTMLAudioElement>((resolve, reject) => {
      const audio = new Audio(url);
      
      const handleLoad = () => {
        this.cache.set(soundFile, audio);
        this.loadingPromises.delete(soundFile);
        resolve(audio);
      };

      const handleError = () => {
        this.loadingPromises.delete(soundFile);
        reject(new Error(`Failed to load audio: ${soundFile}`));
      };

      audio.addEventListener('canplaythrough', handleLoad, { once: true });
      audio.addEventListener('error', handleError, { once: true });
      
      // Start loading
      audio.load();
    });

    this.loadingPromises.set(soundFile, loadingPromise);
    return loadingPromise;
  }

  /**
   * Get a cached audio element, or create a new one if not cached
   */
  getAudio(soundFile: string): HTMLAudioElement {
    if (this.cache.has(soundFile)) {
      return this.cache.get(soundFile)!;
    }

    // If not cached, create a new audio element
    const url = `https://static.bestonlineclock.com/sounds/${soundFile}`;
    const audio = new Audio(url);
    this.cache.set(soundFile, audio);
    return audio;
  }

  /**
   * Create a clone of cached audio for independent playback
   */
  createAudioClone(soundFile: string): HTMLAudioElement {
    const cachedAudio = this.getAudio(soundFile);
    const clone = cachedAudio.cloneNode() as HTMLAudioElement;
    return clone;
  }

  /**
   * Clear the cache
   */
  clear(): void {
    this.cache.forEach(audio => {
      audio.pause();
      audio.currentTime = 0;
    });
    this.cache.clear();
    this.loadingPromises.clear();
  }

  /**
   * Remove a specific audio from cache
   */
  remove(soundFile: string): void {
    const audio = this.cache.get(soundFile);
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      this.cache.delete(soundFile);
    }
  }

  /**
   * Check if audio is cached
   */
  isCached(soundFile: string): boolean {
    return this.cache.has(soundFile);
  }
}

// Export singleton instance
export const audioCache = new AudioCache();
