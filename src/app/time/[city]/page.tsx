import { MainLayout } from "@/components/layout/main-layout";
import { CityTimeSEOContent } from "@/components/seo/city-time-seo-content";
import { StructuredData } from "@/components/seo/structured-data";
import { breadcrumbConfigs } from "@/components/ui/breadcrumb";
import { CityTimeClient } from "./city-time-client";
import { getCityById, getAllCities } from "@/lib/timezone";
import { getCountryFlag } from "@/lib/country-flags";
import { notFound } from "next/navigation";
import { Metadata } from "next";

interface CityTimePageProps {
  params: Promise<{
    city: string;
  }>;
}

// Generate static params for all cities
export async function generateStaticParams() {
  const cities = getAllCities();
  return cities.map((city) => ({
    city: city.id,
  }));
}

// Generate metadata for each city page
export async function generateMetadata({ params }: CityTimePageProps): Promise<Metadata> {
  const { city: cityId } = await params;
  const city = getCityById(cityId);
  
  if (!city) {
    return {
      title: 'City Not Found',
    };
  }

  return {
    title: `Current Time in ${city.name}, ${city.country} - World Clock`,
    description: `What time is it in ${city.name} right now? Current local time in ${city.name}, ${city.country}. Timezone: ${city.timezone}.`,
    alternates: {
      canonical: `https://bestonlineclock.com/time/${city.id}`,
    },
  };
}

export default async function CityTimePage({ params }: CityTimePageProps) {
  const { city: cityId } = await params;
  const city = getCityById(cityId);
  
  if (!city) {
    notFound();
  }

  const countryFlag = getCountryFlag(city.country);
  const breadcrumbItems = breadcrumbConfigs.cityTime(city.name, countryFlag);

  return (
    <MainLayout>
      <StructuredData
        type="city-time"
        cityName={city.name}
        cityCountry={city.country}
        timezone={city.timezone}
      />

      {/* Full-height clock section */}
      <section className="relative" style={{ height: 'calc(100vh - 4rem)' }}>
        <CityTimeClient city={city} />
      </section>

      {/* SEO content section - below the fold */}
      <section className="relative z-9" data-seo-content>
        <CityTimeSEOContent city={city} breadcrumbItems={breadcrumbItems} />
      </section>
    </MainLayout>
  );
}
