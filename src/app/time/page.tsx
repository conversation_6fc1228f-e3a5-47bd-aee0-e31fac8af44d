import { MainLayout } from "@/components/layout/main-layout";
import { WorldTimeSEOContent } from "@/components/seo/world-time-seo-content";
import { StructuredData } from "@/components/seo/structured-data";
import { WorldTimeClient } from "./world-time-client";
import { Metadata } from "next";

// Generate metadata for world time page
export const metadata: Metadata = {
  title: 'World Time Zones - Current Time in Major Cities | Best Online Clock',
  description: 'Display the current time for multiple cities on one screen. Easily customize and select which cities to show for a personalized world clock experience.',
  alternates: {
    canonical: 'https://bestonlineclock.com/time',
  },
};

export default function WorldTimePage() {
  return (
    <MainLayout>
      <StructuredData type="homepage" />

      {/* Full-height clock section */}
      <section className="relative" style={{ height: 'calc(100vh - 4rem)' }}>
        <WorldTimeClient />
      </section>

      {/* SEO content section - below the fold */}
      <section className="relative z-9" data-seo-content>
        <WorldTimeSEOContent />
      </section>
    </MainLayout>
  );
}
