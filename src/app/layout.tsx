import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import <PERSON><PERSON><PERSON> from "next/script";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

/*
export const metadata: Metadata = {
  title: "Online Clock - No Sleep in Fullscreen, Timer & Alarm Tools",
  description: "Best digital clock online! Timer, alarm, stopwatch with customizable display. Fullscreen mode keeps your screen awake—never miss the time.",
  authors: [{ name: "Best Online Clock" }],
  creator: "Best Online Clock",
  publisher: "Best Online Clock",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://bestonlineclock.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Online Clock - Timer, Alarm & Stopwatch Tools (No Sleep in Fullscreen)",
    description: "Best digital clock online! Timer, alarm, stopwatch with customizable display. Fullscreen mode keeps your screen awake—never miss the time.",
    url: 'https://bestonlineclock.com',
    siteName: 'Best Online Clock',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Best Online Clock - Digital Clock Interface',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Online Clock - Timer, Alarm & Stopwatch Tools (No Sleep in Fullscreen)",
    description: "Best digital clock online! Timer, alarm, stopwatch with customizable display. Fullscreen mode keeps your screen awake—never miss the time.",
    images: ['/twitter-image.png'],
    creator: '@bestonlineclock',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};
*/
export const metadata: Metadata = {
  title: "Online Clock - No Sleep in Fullscreen, Timer & Alarm Tools",
  description: "Best digital clock online! Timer, alarm, stopwatch with customizable display. Fullscreen mode keeps your screen awake—never miss the time.",
  authors: [{ name: "Best Online Clock" }],
  creator: "Best Online Clock",
  publisher: "Best Online Clock",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://bestonlineclock.com'),
  alternates: {
    canonical: '/',
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {/* Google tag (gtag.js) */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-6WQVMTCZE1"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-6WQVMTCZE1');
          `}
        </Script>

        {/* Microsoft Clarity */}
        <Script id="microsoft-clarity" strategy="afterInteractive">
          {`
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "rt8e9dvuwh");
          `}
        </Script>
        
        {/* plausible */}
        <Script defer data-domain="bestonlineclock.com" src="https://app.pageview.app/js/script.js" strategy="afterInteractive"></Script>

        {children}
      </body>
    </html>
  );
}
