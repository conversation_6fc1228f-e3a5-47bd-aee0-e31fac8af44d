import Link from 'next/link';
import { Footer } from './Footer';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { City, getTimeOffset, formatTimeOffset, getAllCities } from '@/lib/timezone';

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
}

interface CityTimeSEOContentProps {
  city: City;
  className?: string;
  breadcrumbItems?: BreadcrumbItem[];
}

export function CityTimeSEOContent({ city, breadcrumbItems }: CityTimeSEOContentProps) {
  const timeOffset = getTimeOffset(city);
  const offsetText = formatTimeOffset(timeOffset);
  const allCities = getAllCities();
  const otherCities = allCities.filter(c => c.id !== city.id).slice(0, 6);

  return (
    <div className="w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-4 pt-20 pb-12 space-y-12">
        {/* Breadcrumb Navigation */}
        {breadcrumbItems && (
          <div className="mb-8">
            <Breadcrumb items={breadcrumbItems} />
          </div>
        )}
        {/* Hero Section */}
        <section className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
            What Time is it in {city.name} Right Now?
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
            The current local time in {city.name}, {city.country} is displayed above with real-time updates.
            Our accurate world clock shows the exact time in {city.name} with automatic timezone adjustments
            and daylight saving time support. Perfect for scheduling international meetings, planning travel,
            or staying connected with friends and family across time zones.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <span>✓ Real-time Updates</span>
            <span>✓ Timezone: {city.timezone}</span>
            <span>✓ Offset: {offsetText}</span>
            <span>✓ Accurate & Reliable</span>
          </div>
        </section>

        {/* Quick Time Info */}
        <section className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-center mb-6 text-gray-900 dark:text-white">
            {city.name} Time Right Now - Key Information
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-2">Current Time</h3>
              <p className="text-gray-600 dark:text-gray-300">Live clock above shows exact time in {city.name}</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold text-green-600 dark:text-green-400 mb-2">Time Zone</h3>
              <p className="text-gray-600 dark:text-gray-300">{city.timezone}</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold text-purple-600 dark:text-purple-400 mb-2">UTC Offset</h3>
              <p className="text-gray-600 dark:text-gray-300">UTC{city.utcOffset >= 0 ? '+' : ''}{city.utcOffset}</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold text-orange-600 dark:text-orange-400 mb-2">Time Difference</h3>
              <p className="text-gray-600 dark:text-gray-300">{offsetText} from your time</p>
            </div>
          </div>
        </section>

        {/* Practical Use Cases */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            When You Need {city.name} Time
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                📞 Business Calls
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Schedule calls and meetings with {city.name} during their business hours.
                Avoid calling too early or too late by checking the current time first.
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                ✈️ Flight Planning
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Planning flights to or from {city.name}? Check arrival and departure times
                in local time to avoid confusion and plan your schedule accordingly.
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                🎮 Online Gaming
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Coordinate gaming sessions with friends in {city.name}.
                Know exactly when they&apos;re available to play together.
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                📺 Live Events
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Watch live sports, concerts, or broadcasts from {city.name}.
                Convert event times to know exactly when to tune in.
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                💼 Remote Work
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Working with colleagues in {city.name}? Stay synchronized with their
                work schedule and plan collaborative sessions effectively.
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                👨‍👩‍👧‍👦 Family & Friends
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Stay connected with family and friends in {city.name}.
                Call or message them at convenient times in their local timezone.
              </p>
            </div>
          </div>
        </section>

        {/* Time Zone Information */}
        <section className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            {city.name} Time Zone Information
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🌍 Location Details
              </h3>
              <ul className="text-gray-600 dark:text-gray-300 space-y-2">
                <li><strong>City:</strong> {city.name}</li>
                <li><strong>Country:</strong> {city.country}</li>
                <li><strong>Timezone:</strong> {city.timezone}</li>
                <li><strong>Coordinates:</strong> {city.coordinates.lat}°, {city.coordinates.lng}°</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                ⏰ Time Difference
              </h3>
              <ul className="text-gray-600 dark:text-gray-300 space-y-2">
                <li><strong>Offset from your time:</strong> {offsetText}</li>
                <li><strong>UTC Offset:</strong> UTC{city.utcOffset >= 0 ? '+' : ''}{city.utcOffset}</li>
                <li><strong>DST Offset:</strong> UTC{city.dstOffset >= 0 ? '+' : ''}{city.dstOffset}</li>
                <li><strong>Status:</strong> {timeOffset.isAhead ? 'Ahead of' : 'Behind'} your local time</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🎯 Quick Facts
              </h3>
              <ul className="text-gray-600 dark:text-gray-300 space-y-2">
                <li>• Real-time clock updates every second</li>
                <li>• Accurate timezone calculations</li>
                <li>• Supports 12/24 hour formats</li>
                <li>• Mobile-friendly display</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Why Use Our City Time Clock */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Why Use Our {city.name} Time Clock?
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                🎯 Accurate & Reliable
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Our {city.name} time clock uses precise timezone calculations and updates in real-time. 
                Get the exact current time in {city.name} with automatic daylight saving time adjustments.
              </p>

              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                📱 Mobile Optimized
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Access {city.name} time from any device - desktop, tablet, or smartphone. 
                Our responsive design ensures perfect visibility on all screen sizes.
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                🌐 International Business
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Perfect for scheduling meetings, calls, and events with {city.name}. 
                Know exactly when to connect with colleagues, clients, or friends in {city.country}.
              </p>

              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                ✈️ Travel Planning
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Planning a trip to {city.name}? Check the current local time to help with 
                flight bookings, hotel reservations, and itinerary planning.
              </p>
            </div>
          </div>
        </section>

        {/* Other World Clocks */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Other World Time Zones
          </h2>
          <p className="text-center text-lg text-gray-600 dark:text-gray-300">
            Check the current time in other major cities around the world
          </p>
          <div className="text-center mb-6">
            <Link
              href="/time"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center gap-2"
            >
              🌍 View All World Time Zones
            </Link>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {otherCities.map((otherCity) => (
              <Link
                key={otherCity.id}
                href={`/time/${otherCity.id}`}
                className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600"
              >
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  {otherCity.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {otherCity.country}
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  {otherCity.timezone}
                </p>
              </Link>
            ))}
          </div>
        </section>

        {/* Navigation Links */}
        <section className="text-center">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
            More Online Clock Tools
          </h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/timer"
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Online Timer
            </Link>
            <Link
              href="/alarm"
              className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Alarm Clock
            </Link>
            <Link
              href="/stopwatch"
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Stopwatch
            </Link>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Frequently Asked Questions About {city.name} Time
          </h2>
          <div className="space-y-4 max-w-4xl mx-auto">
            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                What is the current time in {city.name}?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                The current time in {city.name}, {city.country} is displayed above in real-time. 
                Our clock updates every second and automatically adjusts for daylight saving time changes.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                What timezone is {city.name} in?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                {city.name} is in the {city.timezone} timezone. The current UTC offset is 
                UTC{city.utcOffset >= 0 ? '+' : ''}{city.utcOffset}, and during daylight saving time it becomes 
                UTC{city.dstOffset >= 0 ? '+' : ''}{city.dstOffset}.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                How many hours ahead/behind is {city.name} from my time?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                {city.name} is currently {offsetText} compared to your local time. 
                This difference may vary depending on daylight saving time changes in both locations.
              </p>
            </details>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
}
