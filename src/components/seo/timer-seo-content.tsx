import Link from 'next/link';
import { Footer } from './Footer';

export function TimerSEOContent() {
  return (
    <div className="w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-4 pt-20 pb-12 space-y-12">
        {/* Hero Section */}
        <section className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
            Online Timer – No Sleep in Fullscreen, Custom Alerts
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
            Set precise countdown timers with custom durations, visual progress tracking, and audio alerts.
            Perfect for cooking, workouts, study sessions, Pomodoro technique, and productivity timing.
            Works instantly in your browser on any device - no downloads required.
          </p>
        </section>

        {/* Features Grid */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Complete Online Timer Features
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                ⏱️ Custom Timer Setting
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Set precise countdown times with hours, minutes, and seconds.
                Quick preset options for common durations like 1, 5, 10, 15, 30 minutes and 1 hour.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Custom time input (HH:MM:SS)</li>
                <li>• Quick preset buttons</li>
                <li>• Maximum precision timing</li>
                <li>• Easy time adjustment</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🎵 Sound Alerts
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Choose from 22 different notification sounds to alert you when your timer completes.
                Test sounds before setting to find your preferred alert tone.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Morning clock alarms</li>
                <li>• Classic and digital sounds</li>
                <li>• Emergency and warning alerts</li>
                <li>• Gentle ringtones and chimes</li>
                <li>• Nature sounds (birds, rooster)</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🎨 Visual Customization
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Customize the timer display with background colors, images, and positioning.
                Create the perfect visual environment for your timing needs.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Custom background colors</li>
                <li>• Background image upload</li>
                <li>• Timer position adjustment</li>
                <li>• Fullscreen mode support</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                ▶️ Timer Controls
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Full control over your timer with start, pause, and reset functionality.
                Visual progress tracking shows remaining time at a glance.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Start/pause/reset controls</li>
                <li>• Visual countdown display</li>
                <li>• Progress tracking</li>
                <li>• Smooth animations</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                📱 Mobile Responsive
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Optimized for all devices with touch-friendly controls and responsive design.
                Works perfectly on phones, tablets, and desktop computers.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Touch-optimized interface</li>
                <li>• Responsive design</li>
                <li>• Mobile-friendly controls</li>
                <li>• Cross-platform compatibility</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🖥️ Fullscreen Mode
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Distraction-free fullscreen mode for focused timing sessions.
                Perfect for presentations, workouts, and concentration tasks.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Fullscreen timer display</li>
                <li>• Minimal interface</li>
                <li>• Distraction-free mode</li>
                <li>• Easy settings access</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Fullscreen Sleep Prevention Feature (ENGLISH) */}
        <section className="bg-yellow-50 dark:bg-yellow-900/10 rounded-lg p-6 my-8">
          <h3 className="text-xl font-bold text-yellow-800 dark:text-yellow-200 mb-2">⏲️ Fullscreen Sleep Prevention – Timers That Never Stop</h3>
          <p className="text-yellow-900 dark:text-yellow-100">
            Timers are essential for cooking, workouts, study sessions, public speaking, and more. But with most online timers, your computer will eventually go to sleep if you don&apos;t interact with it, and you&apos;ll lose track of your countdown.<br/><br/>
            <b>With our fullscreen timer:</b><br/>
            - The timer display stays on for the entire countdown, no matter how long it runs.<br/>
            - You can focus on your task—cooking, exercising, or studying—without worrying about the screen turning off.<br/>
            - When the timer ends, you&apos;ll see and hear the alert right away, even if you haven&apos;t touched your device.<br/><br/>
            <b>Without this feature:</b><br/>
            - The screen may go black before your timer finishes, and you could miss the end signal.<br/>
            - You might overcook food, miss a workout interval, or lose track of your study session.<br/><br/>
            <b>Our fullscreen sleep prevention ensures your timer is always visible and your results are always accurate—no interruptions, no surprises.</b>
          </p>
        </section>

        {/* Use Cases Section */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Perfect Online Timer for Every Activity
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
                🏢 Work & Productivity
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
                <li>• Pomodoro technique timing (25 min work, 5 min break)</li>
                <li>• Meeting and presentation timers</li>
                <li>• Focus session countdown</li>
                <li>• Break reminder timing</li>
                <li>• Task time boxing</li>
                <li>• Deadline countdown tracking</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
                🍳 Cooking & Kitchen
              </h3>
              <ul className="text-sm text-green-800 dark:text-green-200 space-y-2">
                <li>• Recipe timing and cooking alerts</li>
                <li>• Baking and roasting timers</li>
                <li>• Pasta and rice cooking time</li>
                <li>• Marinating and resting periods</li>
                <li>• Multiple dish coordination</li>
                <li>• Food safety timing</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-purple-900 dark:text-purple-100">
                💪 Fitness & Exercise
              </h3>
              <ul className="text-sm text-purple-800 dark:text-purple-200 space-y-2">
                <li>• Workout interval timing</li>
                <li>• Rest period countdown</li>
                <li>• HIIT training timers</li>
                <li>• Stretching session timing</li>
                <li>• Meditation and mindfulness</li>
                <li>• Exercise routine pacing</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-orange-900 dark:text-orange-100">
                📚 Study & Learning
              </h3>
              <ul className="text-sm text-orange-800 dark:text-orange-200 space-y-2">
                <li>• Study session timing</li>
                <li>• Exam practice countdown</li>
                <li>• Reading time tracking</li>
                <li>• Assignment deadlines</li>
                <li>• Focus technique timing</li>
                <li>• Break scheduling</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-red-900 dark:text-red-100">
                🏠 Daily Life
              </h3>
              <ul className="text-sm text-red-800 dark:text-red-200 space-y-2">
                <li>• Laundry cycle timing</li>
                <li>• Parking meter countdown</li>
                <li>• Game time limits</li>
                <li>• Screen time management</li>
                <li>• Chore timing</li>
                <li>• Activity transitions</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-teal-900 dark:text-teal-100">
                🎯 Special Applications
              </h3>
              <ul className="text-sm text-teal-800 dark:text-teal-200 space-y-2">
                <li>• Photography timing (long exposure)</li>
                <li>• Public speaking practice</li>
                <li>• Therapy session timing</li>
                <li>• Art and craft projects</li>
                <li>• Music practice sessions</li>
                <li>• Competition timing</li>
              </ul>
            </div>
          </div>
        </section>

        {/* How to Use Section */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            How to Use Our Online Timer
          </h2>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Quick Start Guide
              </h3>
              <ol className="space-y-3 text-gray-600 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                  <span>Click the settings panel to open timer configuration options</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                  <span>Set your desired time using custom input or quick preset buttons</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                  <span>Choose your preferred notification sound and test it</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
                  <span>Customize background and position if desired</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">5</span>
                  <span>Click &quot;Set Timer&quot; and then &quot;Start&quot; to begin countdown</span>
                </li>
              </ol>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Pro Tips
              </h3>
              <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">💡</span>
                  <span>Use fullscreen mode for distraction-free timing sessions</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">💡</span>
                  <span>Test notification sounds to ensure you&apos;ll hear the alert</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">💡</span>
                  <span>Bookmark this page for quick access to your timer</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">💡</span>
                  <span>Use preset times for common activities like Pomodoro (25 min)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">💡</span>
                  <span>Pause and resume functionality lets you handle interruptions</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Navigation Links */}
        <section className="text-center">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
            Explore More Time Management Tools
          </h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/time"
              className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              World Time Clock
            </Link>
            <Link
              href="/alarm"
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Alarm Clock
            </Link>
            <Link
              href="/stopwatch"
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Stopwatch
            </Link>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Frequently Asked Questions About Online Timer
          </h2>
          <div className="space-y-4 max-w-4xl mx-auto">
            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                What timer presets are available?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Our timer includes quick preset options for 1 minute, 5 minutes, 10 minutes, 15 minutes,
                30 minutes, and 1 hour. You can also set custom times by specifying exact hours, minutes,
                and seconds for maximum precision timing.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Can I use this timer for Pomodoro technique?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Absolutely! Our timer is perfect for Pomodoro technique with quick 25-minute preset for work sessions
                and 5-minute preset for breaks. The audio alerts ensure you know when to switch between work and rest periods.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Does the timer work when the browser tab is not active?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes, our timer continues running in the background even when you switch to other tabs or applications.
                You&apos;ll still receive audio notifications when the timer completes, ensuring you never miss an alert.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Can I customize the timer appearance?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes, you can customize the timer with background colors, upload background images, and adjust the timer position.
                These settings are saved automatically for future use, creating your personalized timer experience.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                What notification sounds are available?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Choose from 22 different notification sounds including morning clock alarms, classic and digital sounds,
                emergency and warning alerts, casino and game sounds, gentle ringtones and chimes, and nature sounds like birds singing and rooster crowing.
                You can test each sound before setting your timer to find your preferred alert tone.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Is this online timer free to use?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes, our online timer is completely free to use with no registration required. Access all features including
                custom timing, multiple sounds, visual customization, and fullscreen mode without any cost or limitations.
              </p>
            </details>
          </div>
        </section>

        {/* Footer Content */}
        <section className="text-center text-gray-600 dark:text-gray-400 border-t pt-8 space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              Best Online Timer - Precise Countdown Timer for Every Need
            </h3>
            <p className="mb-4 max-w-3xl mx-auto">
              Experience the most reliable and feature-rich online timer available. Perfect for cooking, workouts, study sessions,
              Pomodoro technique, and any activity requiring precise timing. With customizable alerts, visual progress tracking,
              and fullscreen mode, our timer adapts to your specific needs. Works instantly in any browser on any device.
            </p>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
}