import Link from 'next/link';
import { Footer } from './Footer';

export function StopwatchSEOContent() {
  return (
    <div className="w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-4 pt-20 pb-12 space-y-12">
        {/* Hero Section */}
        <section className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
            Online Stopwatch – Precision Timing, Laps & Data Export
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
            Professional stopwatch with millisecond precision, lap timing, and customizable display formats.
            Perfect for sports timing, workouts, racing, competitions, and any activity requiring accurate time measurement.
            Works instantly in your browser on any device - no downloads required.
          </p>
        </section>

        {/* Features Grid */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Complete Online Stopwatch Features
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                ⏱️ Precision Timing
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Millisecond-accurate timing with multiple display formats.
                Choose from various time formats to match your specific timing needs.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Millisecond precision (00:00.000)</li>
                <li>• Centisecond format (00:00.00)</li>
                <li>• Decisecond format (00:00.0)</li>
                <li>• Second-only format (00:00)</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                📊 Lap Timing
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Record unlimited lap times with detailed statistics and analysis.
                Perfect for tracking performance and identifying patterns.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Unlimited lap recording</li>
                <li>• Lap time statistics</li>
                <li>• Best/worst lap tracking</li>
                <li>• Average lap calculation</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                💾 Data Export
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Export lap times and timing data for analysis and record keeping.
                Save your timing sessions for future reference.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• CSV export functionality</li>
                <li>• Lap data download</li>
                <li>• Clear data options</li>
                <li>• Session management</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🎨 Visual Customization
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Customize the stopwatch display with fonts, colors, and positioning.
                Create the perfect visual setup for your timing environment.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Custom font families</li>
                <li>• Adjustable font sizes</li>
                <li>• Color customization</li>
                <li>• Position adjustment</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                📱 Mobile Optimized
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Touch-friendly interface optimized for mobile devices and tablets.
                Large buttons and responsive design for easy operation.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Touch-optimized controls</li>
                <li>• Responsive design</li>
                <li>• Large button interface</li>
                <li>• Mobile-friendly layout</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🖥️ Fullscreen Mode
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Distraction-free fullscreen mode for focused timing sessions.
                Perfect for competitions, presentations, and professional use.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Fullscreen display</li>
                <li>• Minimal interface</li>
                <li>• Distraction-free mode</li>
                <li>• Professional presentation</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Fullscreen Sleep Prevention Feature (ENGLISH) */}
        <section className="bg-yellow-50 dark:bg-yellow-900/10 rounded-lg p-6 my-8">
          <h3 className="text-xl font-bold text-yellow-800 dark:text-yellow-200 mb-2">⏱️ Fullscreen Sleep Prevention – Accurate Timing, No Blackouts</h3>
          <p className="text-yellow-900 dark:text-yellow-100">
            For sports, experiments, classroom competitions, or any activity that needs long, uninterrupted timing, nothing is worse than your computer going to sleep and losing your stopwatch display.<br/><br/>
            <b>With our fullscreen stopwatch:</b><br/>
            - The stopwatch stays visible and running, even if you don&apos;t touch your device for a long time.<br/>
            - You can track races, science experiments, or group activities with confidence, knowing the time will always be on screen.<br/>
            - No more losing data or missing split times because the screen turned off.<br/><br/>
            <b>Without this feature:</b><br/>
            - The stopwatch may pause or disappear if your device sleeps, ruining your timing and results.<br/>
            - You risk losing important data or having to start over.<br/><br/>
            <b>Our fullscreen sleep prevention keeps your stopwatch running and visible—so every second is counted, and your timing is always reliable.</b>
          </p>
        </section>

        {/* Use Cases Section */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Perfect Online Stopwatch for Every Activity
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
                🏃 Sports & Athletics
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
                <li>• Track and field timing</li>
                <li>• Swimming lap times</li>
                <li>• Running and marathon timing</li>
                <li>• Cycling performance tracking</li>
                <li>• Race timing and splits</li>
                <li>• Athletic competition timing</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
                💪 Fitness & Training
              </h3>
              <ul className="text-sm text-green-800 dark:text-green-200 space-y-2">
                <li>• Workout interval timing</li>
                <li>• HIIT training sessions</li>
                <li>• Exercise duration tracking</li>
                <li>• Rest period measurement</li>
                <li>• Personal training timing</li>
                <li>• Fitness challenge timing</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-purple-900 dark:text-purple-100">
                🏁 Racing & Competition
              </h3>
              <ul className="text-sm text-purple-800 dark:text-purple-200 space-y-2">
                <li>• Car racing lap times</li>
                <li>• Motorcycle track timing</li>
                <li>• Go-kart racing</li>
                <li>• Drone racing timing</li>
                <li>• Competitive gaming</li>
                <li>• Speed competitions</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-orange-900 dark:text-orange-100">
                🎯 Professional Use
              </h3>
              <ul className="text-sm text-orange-800 dark:text-orange-200 space-y-2">
                <li>• Scientific experiments</li>
                <li>• Laboratory timing</li>
                <li>• Process timing analysis</li>
                <li>• Quality control testing</li>
                <li>• Performance benchmarking</li>
                <li>• Time and motion studies</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-red-900 dark:text-red-100">
                🎮 Gaming & Entertainment
              </h3>
              <ul className="text-sm text-red-800 dark:text-red-200 space-y-2">
                <li>• Speedrun timing</li>
                <li>• Game challenge timing</li>
                <li>• Puzzle solving races</li>
                <li>• Board game timing</li>
                <li>• Quiz competition timing</li>
                <li>• Entertainment events</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-teal-900 dark:text-teal-100">
                📚 Education & Learning
              </h3>
              <ul className="text-sm text-teal-800 dark:text-teal-200 space-y-2">
                <li>• Exam timing practice</li>
                <li>• Reading speed tests</li>
                <li>• Presentation timing</li>
                <li>• Study session tracking</li>
                <li>• Classroom activities</li>
                <li>• Learning assessments</li>
              </ul>
            </div>
          </div>
        </section>

        {/* How to Use Section */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            How to Use Our Online Stopwatch
          </h2>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Basic Operation
              </h3>
              <ol className="space-y-3 text-gray-600 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                  <span>Click &quot;Start&quot; to begin timing your activity</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                  <span>Use &quot;Lap&quot; button to record split times during timing</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                  <span>Click &quot;Stop&quot; to pause timing, &quot;Start&quot; again to resume</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
                  <span>Use &quot;Reset&quot; to clear the timer and start fresh</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">5</span>
                  <span>Export lap data or clear records as needed</span>
                </li>
              </ol>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Advanced Features
              </h3>
              <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">⚙️</span>
                  <span>Customize time format for your specific needs</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">🎨</span>
                  <span>Adjust colors, fonts, and display position</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">📊</span>
                  <span>View lap statistics and performance analysis</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">💾</span>
                  <span>Export timing data for external analysis</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">🖥️</span>
                  <span>Use fullscreen mode for distraction-free timing</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Navigation Links */}
        <section className="text-center">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
            Explore More Time Management Tools
          </h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/time"
              className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              World Time Clock
            </Link>
            <Link
              href="/timer"
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Online Timer
            </Link>
            <Link
              href="/alarm"
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Alarm Clock
            </Link>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Frequently Asked Questions About Online Stopwatch
          </h2>
          <div className="space-y-4 max-w-4xl mx-auto">
            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                How accurate is this online stopwatch?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Our stopwatch provides millisecond-level accuracy, making it suitable for most timing applications.
                The precision depends on your device and browser, but typically offers accuracy within 1-10 milliseconds
                for most modern devices and browsers.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Can I record lap times with this stopwatch?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes! You can record unlimited lap times during your timing session. The stopwatch displays
                lap statistics including best lap, worst lap, and average lap time. You can also export
                all lap data to CSV format for further analysis.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                What time formats are available?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Choose from four time formats: milliseconds (00:00.000), centiseconds (00:00.00),
                deciseconds (00:00.0), or seconds only (00:00). Select the format that best matches
                your timing requirements and precision needs.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Does the stopwatch work when the browser tab is not active?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes, our stopwatch continues running accurately even when you switch to other tabs or applications.
                The timing mechanism is designed to maintain precision regardless of browser tab activity,
                ensuring reliable timing for your activities.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Can I customize the stopwatch appearance?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Absolutely! Customize the font family, font size, text color, background color, and position.
                You can also upload background images and adjust the display position. All settings are
                automatically saved for future use.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Is this online stopwatch free to use?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes, our online stopwatch is completely free to use with no registration required. Access all features
                including lap timing, data export, customization options, and fullscreen mode without any cost or limitations.
              </p>
            </details>
          </div>
        </section>

        {/* Footer Content */}
        <section className="text-center text-gray-600 dark:text-gray-400 border-t pt-8 space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              Best Online Stopwatch - Professional Precision Timing Tool
            </h3>
            <p className="mb-4 max-w-3xl mx-auto">
              Experience the most accurate and feature-rich online stopwatch available. Perfect for sports timing, fitness tracking,
              racing, competitions, and any activity requiring precise time measurement. With lap timing, data export, and full
              customization options, our stopwatch adapts to your specific timing needs. Works instantly in any browser on any device.
            </p>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
}