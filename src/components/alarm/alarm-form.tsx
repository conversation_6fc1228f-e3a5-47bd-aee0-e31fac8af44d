"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Play, Check, AlertCircle, Square } from "lucide-react";
import { audioCache } from "@/utils/audio-cache";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
}

interface AlarmFormProps {
  onAddAlarm: (alarm: Alarm) => boolean; // Return boolean to indicate success/failure
  onTestAlarm: (alarm: Omit<Alarm, 'id' | 'enabled'>) => void;
  existingAlarms: Alarm[];
}

export function AlarmForm({ onAddAlarm, onTestAlarm, existingAlarms }: AlarmFormProps) {
  const [time, setTime] = useState("08:00");
  const [title, setTitle] = useState("Alarm");
  const [sound, setSound] = useState("classic-alarm.wav");
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState("");
  const [isPlayingPreview, setIsPlayingPreview] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Preload audio when sound changes
  useEffect(() => {
    if (sound) {
      audioCache.preload(sound).catch(error => {
        console.error("Failed to preload sound:", error);
      });
    }
  }, [sound]);

  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Check for duplicate time (only for non-snoozed alarms)
    const isDuplicate = existingAlarms.some(alarm =>
      alarm.time === time && !alarm.title.includes('(Snoozed)')
    );
    if (isDuplicate) {
      setError(`An alarm for ${time} already exists!`);
      return;
    }

    const newAlarm = {
      id: Date.now().toString(),
      time,
      title,
      sound,
      enabled: true,
    };

    const success = onAddAlarm(newAlarm);

    if (success) {
      // Show success state
      setIsSuccess(true);

      // Reset form
      setTime("08:00");
      setTitle("Alarm");
      setSound("classic-alarm.wav");

      // Reset success state after 2 seconds
      setTimeout(() => {
        setIsSuccess(false);
      }, 2000);
    }
  };

  const handleTest = () => {
    onTestAlarm({ time, title, sound });
  };

  // Sound preview functionality
  const handlePlayPreview = async () => {
    try {
      if (isPlayingPreview) {
        // Stop current playback
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current.currentTime = 0;
        }
        setIsPlayingPreview(false);
        return;
      }

      // Preload audio if not cached
      await audioCache.preload(sound);

      // Create a new audio instance for preview to avoid conflicts
      audioRef.current = audioCache.createAudioClone(sound);

      // Set up event listeners
      const handleEnded = () => {
        setIsPlayingPreview(false);
      };

      const handleError = () => {
        console.error("Failed to play preview sound");
        setIsPlayingPreview(false);
      };

      audioRef.current.addEventListener('ended', handleEnded);
      audioRef.current.addEventListener('error', handleError);

      // Play the audio
      const playPromise = audioRef.current.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          setIsPlayingPreview(true);

          // Stop playing after 3 seconds for preview
          setTimeout(() => {
            if (audioRef.current && isPlayingPreview) {
              audioRef.current.pause();
              audioRef.current.currentTime = 0;
              setIsPlayingPreview(false);
            }
          }, 3000);
        }).catch((error) => {
          console.error("Failed to play preview sound:", error);
          setIsPlayingPreview(false);
        });
      }
    } catch (error) {
      console.error("Failed to handle preview:", error);
      setIsPlayingPreview(false);
    }
  };

  const soundOptions = [
    { value: "morning-clock-alarm.wav", label: "Morning Clock Alarm" },
    { value: "classic-alarm.wav", label: "Classic Alarm" },
    { value: "digital-alarm-buzzer.wav", label: "Digital Alarm Buzzer" },
    { value: "vintage-warning-alarm.wav", label: "Vintage Warning Alarm" },
    { value: "warning-alarm-buzzer.wav", label: "Warning Alarm Buzzer" },
    { value: "alert-alarm.wav", label: "Alert Alarm" },
    { value: "emergency-alert-alarm.wav", label: "Emergency Alert Alarm" },
    { value: "hall-alert-sound.wav", label: "Hall Alert Sound" },
    { value: "facility-alarm-sound.wav", label: "Facility Alarm Sound" },
    { value: "retro-game-emergency-alarm.wav", label: "Retro Game Emergency Alarm" },
    { value: "battleship-alarm.wav", label: "Battleship Alarm" },
    { value: "city-alert-siren-loop.wav", label: "City Alert Siren Loop" },
    { value: "street-public-alarm.wav", label: "Street Public Alarm" },
    { value: "security-facility-breach-alarm.wav", label: "Security Facility Breach Alarm" },
    { value: "casino-jackpot-alarm.wav", label: "Casino Jackpot Alarm" },
    { value: "classic-winner-alarm.wav", label: "Classic Winner Alarm" },
    { value: "slot-machine-win-alarm.wav", label: "Slot Machine Win Alarm" },
    { value: "slot-machine-payout-alarm.wav", label: "Slot Machine Payout Alarm" },
    { value: "marimba-ringtone.wav", label: "Marimba Ringtone" },
    { value: "vintage-telephone-ringtone.wav", label: "Vintage Telephone Ringtone" },
    { value: "rooster-crowing.wav", label: "Rooster Crowing" },
    { value: "birds-singing.wav", label: "Birds Singing" },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="time" className="block text-sm font-medium mb-1">
          Time
        </label>
        <input
          type="time"
          id="time"
          value={time}
          onChange={(e) => setTime(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
          required
        />
      </div>

      <div>
        <label htmlFor="title" className="block text-sm font-medium mb-1">
          Title
        </label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
          required
        />
      </div>

      <div>
        <label htmlFor="sound" className="block text-sm font-medium mb-1">
          Sound
        </label>
        <div className="flex gap-2">
          <Select value={sound} onValueChange={setSound}>
            <SelectTrigger id="sound" className="flex-1">
              <SelectValue placeholder="Select sound" />
            </SelectTrigger>
            <SelectContent>
              {soundOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            type="button"
            variant="outline"
            onClick={handlePlayPreview}
            className="h-10 w-10 p-0 border-gray-300 dark:border-gray-600 hover:bg-purple-50 hover:border-purple-300 dark:hover:bg-purple-900/20 rounded-md"
          >
            {isPlayingPreview ? (
              <Square size={16} className="text-purple-600" />
            ) : (
              <Play size={16} className="text-purple-600" />
            )}
          </Button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <AlertCircle size={16} className="text-red-600 dark:text-red-400" />
          <span className="text-sm text-red-600 dark:text-red-400">{error}</span>
        </div>
      )}

      {/* Test button */}
      <Button
        type="button"
        onClick={handleTest}
        variant="outline"
        className="w-full"
      >
        <Play size={16} className="mr-2" />
        Test Alarm
      </Button>

      {/* Add alarm button */}
      <Button
        type="submit"
        className={`w-full transition-all duration-300 ${
          isSuccess
            ? 'bg-green-600 hover:bg-green-700 text-white'
            : ''
        }`}
      >
        {isSuccess ? (
          <>
            <Check size={16} className="mr-2" />
            Alarm Added Successfully!
          </>
        ) : (
          'Add Alarm'
        )}
      </Button>
    </form>
  );
}
