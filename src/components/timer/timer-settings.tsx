"use client";

import { useState, useEffect, useRef } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Settings, X, Clock, Volume2, Palette, Move, Play, Square } from "lucide-react";

interface TimerSettingsProps {
  onSetTimer: (time: number, sound: string) => void;
  sound: string;
  setSound: (sound: string) => void;
  backgroundColor: string;
  setBackgroundColor: (color: string) => void;
  backgroundImage: string;
  setBackgroundImage: (image: string) => void;
  position: { x: number; y: number };
  setPosition: (position: { x: number; y: number }) => void;
}

export function TimerSettings({
  onSetTimer,
  sound,
  setSound,
  backgroundColor,
  setBackgroundColor,
  backgroundImage,
  setBackgroundImage,
  position,
  setPosition
}: TimerSettingsProps) {
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [isPlayingPreview, setIsPlayingPreview] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Set mounted state and detect mobile
  useEffect(() => {
    setIsMounted(true);

    const updateMobileState = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      // Auto collapse on mobile
      if (mobile) {
        setIsCollapsed(true);
      }
    };

    updateMobileState();
    window.addEventListener('resize', updateMobileState);

    return () => {
      window.removeEventListener('resize', updateMobileState);
    };
  }, []);

  // Handle fullscreen changes (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    const handleFullScreenChange = () => {
      if (document.fullscreenElement) {
        setIsCollapsed(true);
      }
    };

    document.addEventListener('fullscreenchange', handleFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
    };
  }, [isMounted]);

  const soundOptions = [
    { value: "morning-clock-alarm.wav", label: "Morning Clock Alarm" },
    { value: "classic-alarm.wav", label: "Classic Alarm" },
    { value: "digital-alarm-buzzer.wav", label: "Digital Alarm Buzzer" },
    { value: "vintage-warning-alarm.wav", label: "Vintage Warning Alarm" },
    { value: "warning-alarm-buzzer.wav", label: "Warning Alarm Buzzer" },
    { value: "alert-alarm.wav", label: "Alert Alarm" },
    { value: "emergency-alert-alarm.wav", label: "Emergency Alert Alarm" },
    { value: "hall-alert-sound.wav", label: "Hall Alert Sound" },
    { value: "facility-alarm-sound.wav", label: "Facility Alarm Sound" },
    { value: "retro-game-emergency-alarm.wav", label: "Retro Game Emergency Alarm" },
    { value: "battleship-alarm.wav", label: "Battleship Alarm" },
    { value: "city-alert-siren-loop.wav", label: "City Alert Siren Loop" },
    { value: "street-public-alarm.wav", label: "Street Public Alarm" },
    { value: "security-facility-breach-alarm.wav", label: "Security Facility Breach Alarm" },
    { value: "casino-jackpot-alarm.wav", label: "Casino Jackpot Alarm" },
    { value: "classic-winner-alarm.wav", label: "Classic Winner Alarm" },
    { value: "slot-machine-win-alarm.wav", label: "Slot Machine Win Alarm" },
    { value: "slot-machine-payout-alarm.wav", label: "Slot Machine Payout Alarm" },
    { value: "marimba-ringtone.wav", label: "Marimba Ringtone" },
    { value: "vintage-telephone-ringtone.wav", label: "Vintage Telephone Ringtone" },
    { value: "rooster-crowing.wav", label: "Rooster Crowing" },
    { value: "birds-singing.wav", label: "Birds Singing" },
  ];

  const presetTimes = [
    { label: "1 min", time: 60 * 1000 },
    { label: "3 min", time: 3 * 60 * 1000 },
    { label: "5 min", time: 5 * 60 * 1000 },
    { label: "10 min", time: 10 * 60 * 1000 },
    { label: "15 min", time: 15 * 60 * 1000 },
    { label: "30 min", time: 30 * 60 * 1000 },
    { label: "45 min", time: 45 * 60 * 1000 },
    { label: "1 hour", time: 60 * 60 * 1000 },
  ];

  const mobileTabsConfig = [
    {
      id: 0,
      label: "Timer",
      icon: Clock,
      color: "text-blue-600"
    },
    {
      id: 1,
      label: "Sound",
      icon: Volume2,
      color: "text-purple-600"
    },
    {
      id: 2,
      label: "Background",
      icon: Palette,
      color: "text-green-600"
    },
    {
      id: 3,
      label: "Position",
      icon: Move,
      color: "text-orange-600"
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const totalMilliseconds = (hours * 3600 + minutes * 60 + seconds) * 1000;
    if (totalMilliseconds > 0) {
      onSetTimer(totalMilliseconds, sound);
    }
  };

  const handlePresetClick = (time: number) => {
    onSetTimer(time, sound);
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const moveTimer = (direction: 'up' | 'down' | 'left' | 'right', e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    const step = 20;
    const currentPos = position || { x: 0, y: 0 };
    let newPos;
    switch (direction) {
      case 'up': newPos = { ...currentPos, y: currentPos.y - step }; break;
      case 'down': newPos = { ...currentPos, y: currentPos.y + step }; break;
      case 'left': newPos = { ...currentPos, x: currentPos.x - step }; break;
      case 'right': newPos = { ...currentPos, x: currentPos.x + step }; break;
      default: newPos = currentPos;
    }
    setPosition(newPos);
  };

  // Sound preview functionality
  const handlePlayPreview = () => {
    if (!audioRef.current) {
      audioRef.current = new Audio(`https://static.bestonlineclock.com/sounds/${sound}`);
    } else {
      audioRef.current.src = `https://static.bestonlineclock.com/sounds/${sound}`;
    }

    if (isPlayingPreview) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlayingPreview(false);
    } else {
      audioRef.current.play().then(() => {
        setIsPlayingPreview(true);
      }).catch((error) => {
        console.error("Failed to play preview sound:", error);
      });

      // Stop playing after 3 seconds for preview
      setTimeout(() => {
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current.currentTime = 0;
          setIsPlayingPreview(false);
        }
      }, 3000);
    }
  };

  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, []);

  // Mobile tab content components
  const renderMobileTabContent = () => {
    switch (activeTab) {
      case 0: // Timer
        return (
          <div className="space-y-4">
            {/* Custom Timer Form */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Custom Timer</h4>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-3 gap-3">
                  <div>
                    <label htmlFor="hours" className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Hours
                    </label>
                    <input
                      type="number"
                      id="hours"
                      min="0"
                      max="23"
                      value={hours}
                      onChange={(e) => setHours(parseInt(e.target.value) || 0)}
                      className="w-full h-11 px-3 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label htmlFor="minutes" className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Minutes
                    </label>
                    <input
                      type="number"
                      id="minutes"
                      min="0"
                      max="59"
                      value={minutes}
                      onChange={(e) => setMinutes(parseInt(e.target.value) || 0)}
                      className="w-full h-11 px-3 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label htmlFor="seconds" className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Seconds
                    </label>
                    <input
                      type="number"
                      id="seconds"
                      min="0"
                      max="59"
                      value={seconds}
                      onChange={(e) => setSeconds(parseInt(e.target.value) || 0)}
                      className="w-full h-11 px-3 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                </div>

                <Button type="submit" className="w-full h-11 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-medium transition-colors duration-200">
                  Set Timer
                </Button>
              </form>
            </div>

            {/* Preset Times */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Quick Presets</h4>
              <div className="grid grid-cols-2 gap-2">
                {presetTimes.map((preset) => (
                  <Button
                    key={preset.label}
                    variant="outline"
                    onClick={() => handlePresetClick(preset.time)}
                    className="h-11 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl text-sm"
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        );

      case 1: // Sound
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Completion Sound
              </label>
              <div className="flex gap-2">
                <Select value={sound} onValueChange={setSound}>
                  <SelectTrigger className="h-11 bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 rounded-xl flex-1">
                    <SelectValue placeholder="Select sound" />
                  </SelectTrigger>
                  <SelectContent>
                    {soundOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  onClick={handlePlayPreview}
                  className="h-11 w-11 p-0 border-gray-300 dark:border-gray-600 hover:bg-purple-50 hover:border-purple-300 dark:hover:bg-purple-900/20 rounded-xl"
                >
                  {isPlayingPreview ? (
                    <Square size={16} className="text-purple-600" />
                  ) : (
                    <Play size={16} className="text-purple-600" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        );

      case 2: // Background
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Background Color
              </label>
              <div className="flex gap-3">
                <div className="relative flex-1">
                  <input
                    type="color"
                    value={backgroundColor || "#ffffff"}
                    onChange={(e) => {
                      setBackgroundColor(e.target.value);
                      setBackgroundImage('');
                    }}
                    className="w-full h-11 rounded-xl border-2 border-gray-200 dark:border-gray-700 cursor-pointer"
                  />
                  <div className="absolute inset-0 rounded-xl border-2 border-gray-200 dark:border-gray-700 pointer-events-none"></div>
                </div>
                <button
                  onClick={() => setBackgroundColor('')}
                  className="px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900/30 dark:hover:bg-red-900/50 dark:text-red-400 rounded-xl text-sm font-medium transition-colors duration-200"
                >
                  Clear
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Image URL
              </label>
              <input
                type="text"
                value={backgroundImage}
                onChange={(e) => {
                  setBackgroundImage(e.target.value);
                  setBackgroundColor('');
                }}
                placeholder="Enter image URL"
                className="w-full h-11 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Upload Image
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                      const base64String = event.target?.result as string;
                      setBackgroundImage(base64String);
                      setBackgroundColor('');
                    };
                    reader.readAsDataURL(file);
                  }
                }}
                className="w-full h-11 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm file:mr-3 file:py-2 file:px-3 file:rounded-lg file:border-0 file:text-sm file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 cursor-pointer"
              />
            </div>

            <button
              onClick={() => {
                setBackgroundImage('');
                setBackgroundColor('');
              }}
              className="w-full py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300 rounded-xl text-sm font-medium transition-colors duration-200"
            >
              Clear All Backgrounds
            </button>
          </div>
        );

      case 3: // Position
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-2">
              <div></div>
              <Button
                variant="outline"
                onClick={(e) => moveTimer('up', e)}
                className="h-11 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
              >
                ↑
              </Button>
              <div></div>

              <Button
                variant="outline"
                onClick={(e) => moveTimer('left', e)}
                className="h-11 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
              >
                ←
              </Button>
              <Button
                variant="outline"
                onClick={() => setPosition({ x: 0, y: 0 })}
                className="h-11 text-xs border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl font-medium"
              >
                Center
              </Button>
              <Button
                variant="outline"
                onClick={(e) => moveTimer('right', e)}
                className="h-11 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
              >
                →
              </Button>

              <div></div>
              <Button
                variant="outline"
                onClick={(e) => moveTimer('down', e)}
                className="h-11 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
              >
                ↓
              </Button>
              <div></div>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center gap-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg text-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                Position: ({position?.x || 0}, {position?.y || 0})
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* Floating Settings Button */}
      {isCollapsed && (
        <div
          className="group"
          style={{
            position: 'fixed',
            bottom: '24px',
            right: '24px',
            zIndex: 50,
          }}
        >
          <button
            onClick={toggleCollapse}
            className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group-hover:scale-110"
          >
            <Settings size={22} className="transition-transform duration-300 group-hover:rotate-90" />
          </button>
        </div>
      )}

      {/* Settings Panel */}
      {!isCollapsed && (
        <div
          className={`${
            isMobile 
              ? 'bg-white/50 dark:bg-gray-900/50 backdrop-blur-md border border-white/30 dark:border-gray-700/30 shadow-xl w-full h-[50vh] rounded-t-3xl'
              : 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border border-white/20 dark:border-gray-700/50 shadow-2xl w-96 h-[90vh] rounded-3xl'
          } flex flex-col`}
          style={{
            position: 'fixed',
            zIndex: 50,
            ...(isMobile ? {
              bottom: '0px',
              left: '0px',
              right: '0px',
            } : {
              bottom: '24px',
              right: '24px',
            }),
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Settings size={16} className="text-white" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Timer Settings
              </h2>
            </div>
            <button
              onClick={toggleCollapse}
              className="w-8 h-8 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center justify-center transition-colors duration-200"
            >
              <X size={18} className="text-gray-500 dark:text-gray-400" />
            </button>
          </div>

          {/* Mobile Tabs */}
          {isMobile && (
            <div className="flex border-b border-gray-200/50 dark:border-gray-700/50">
              {mobileTabsConfig.map((tab) => {
                const IconComponent = tab.icon;
                const isActive = activeTab === tab.id;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 flex flex-col items-center gap-1 py-3 px-2 transition-all duration-200 ${
                      isActive
                        ? 'bg-blue-50 dark:bg-blue-900/20 border-b-2 border-blue-500'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
                    }`}
                  >
                    <IconComponent 
                      size={16} 
                      className={isActive ? 'text-blue-600' : 'text-gray-500 dark:text-gray-400'} 
                    />
                    <span className={`text-xs font-medium ${
                      isActive ? 'text-blue-600' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {tab.label}
                    </span>
                  </button>
                );
              })}
            </div>
          )}

          {/* Content */}
          {isMobile ? (
            // Mobile tabbed content
            <div className="flex-1 overflow-y-auto p-6">
              {renderMobileTabContent()}
            </div>
          ) : (
            // Desktop content
            <div className="flex-1 overflow-y-auto p-6 space-y-8">
              
              {/* Timer Settings */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Clock size={18} className="text-blue-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Timer</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4 space-y-4">
                  {/* Custom Timer Form */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Custom Timer</h4>
                    <form onSubmit={handleSubmit} className="space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <label htmlFor="hours" className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                            Hours
                          </label>
                          <input
                            type="number"
                            id="hours"
                            min="0"
                            max="23"
                            value={hours}
                            onChange={(e) => setHours(parseInt(e.target.value) || 0)}
                            className="w-full h-12 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          />
                        </div>

                        <div>
                          <label htmlFor="minutes" className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                            Minutes
                          </label>
                          <input
                            type="number"
                            id="minutes"
                            min="0"
                            max="59"
                            value={minutes}
                            onChange={(e) => setMinutes(parseInt(e.target.value) || 0)}
                            className="w-full h-12 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          />
                        </div>

                        <div>
                          <label htmlFor="seconds" className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                            Seconds
                          </label>
                          <input
                            type="number"
                            id="seconds"
                            min="0"
                            max="59"
                            value={seconds}
                            onChange={(e) => setSeconds(parseInt(e.target.value) || 0)}
                            className="w-full h-12 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          />
                        </div>
                      </div>

                      <Button type="submit" className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-medium transition-colors duration-200">
                        Set Timer
                      </Button>
                    </form>
                  </div>

                  {/* Preset Times */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Quick Presets</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {presetTimes.map((preset) => (
                        <Button
                          key={preset.label}
                          variant="outline"
                          onClick={() => handlePresetClick(preset.time)}
                          className="h-12 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
                        >
                          {preset.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Sound Settings */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Volume2 size={18} className="text-purple-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Sound</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Completion Sound
                    </label>
                    <div className="flex gap-3">
                      <Select value={sound} onValueChange={setSound}>
                        <SelectTrigger className="h-12 bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 rounded-xl flex-1">
                          <SelectValue placeholder="Select sound" />
                        </SelectTrigger>
                        <SelectContent>
                          {soundOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        onClick={handlePlayPreview}
                        className="h-12 w-12 p-0 border-gray-300 dark:border-gray-600 hover:bg-purple-50 hover:border-purple-300 dark:hover:bg-purple-900/20 rounded-xl"
                      >
                        {isPlayingPreview ? (
                          <Square size={18} className="text-purple-600" />
                        ) : (
                          <Play size={18} className="text-purple-600" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Background */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Palette size={18} className="text-green-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Background</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Background Color
                    </label>
                    <div className="flex gap-3">
                      <div className="relative flex-1">
                        <input
                          type="color"
                          value={backgroundColor || "#ffffff"}
                          onChange={(e) => {
                            setBackgroundColor(e.target.value);
                            setBackgroundImage('');
                          }}
                          className="w-full h-12 rounded-xl border-2 border-gray-200 dark:border-gray-700 cursor-pointer"
                        />
                        <div className="absolute inset-0 rounded-xl border-2 border-gray-200 dark:border-gray-700 pointer-events-none"></div>
                      </div>
                      <button
                        onClick={() => setBackgroundColor('')}
                        className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900/30 dark:hover:bg-red-900/50 dark:text-red-400 rounded-xl text-sm font-medium transition-colors duration-200"
                      >
                        Clear
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Image URL
                    </label>
                    <input
                      type="text"
                      value={backgroundImage}
                      onChange={(e) => {
                        setBackgroundImage(e.target.value);
                        setBackgroundColor('');
                      }}
                      placeholder="Enter image URL"
                      className="w-full h-12 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Upload Image
                    </label>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const reader = new FileReader();
                          reader.onload = (event) => {
                            const base64String = event.target?.result as string;
                            setBackgroundImage(base64String);
                            setBackgroundColor('');
                          };
                          reader.readAsDataURL(file);
                        }
                      }}
                      className="w-full h-12 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm file:mr-3 file:py-2 file:px-3 file:rounded-lg file:border-0 file:text-sm file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 cursor-pointer"
                    />
                  </div>

                  <button
                    onClick={() => {
                      setBackgroundImage('');
                      setBackgroundColor('');
                    }}
                    className="w-full py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300 rounded-xl text-sm font-medium transition-colors duration-200"
                  >
                    Clear All Backgrounds
                  </button>
                </div>
              </div>

              {/* Position Controls */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Move size={18} className="text-orange-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Position</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4 space-y-4">
                  <div className="grid grid-cols-3 gap-2">
                    <div></div>
                    <Button
                      variant="outline"
                      onClick={(e) => moveTimer('up', e)}
                      className="h-12 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
                    >
                      ↑
                    </Button>
                    <div></div>

                    <Button
                      variant="outline"
                      onClick={(e) => moveTimer('left', e)}
                      className="h-12 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
                    >
                      ←
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setPosition({ x: 0, y: 0 })}
                      className="h-12 text-xs border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl font-medium"
                    >
                      Center
                    </Button>
                    <Button
                      variant="outline"
                      onClick={(e) => moveTimer('right', e)}
                      className="h-12 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
                    >
                      →
                    </Button>

                    <div></div>
                    <Button
                      variant="outline"
                      onClick={(e) => moveTimer('down', e)}
                      className="h-12 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
                    >
                      ↓
                    </Button>
                    <div></div>
                  </div>

                  <div className="text-center">
                    <div className="inline-flex items-center gap-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg text-sm">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Position: ({position?.x || 0}, {position?.y || 0})
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className={`border-t border-gray-200/50 dark:border-gray-700/50 ${isMobile ? 'p-4' : 'p-6'}`}>
            <button
              onClick={toggleCollapse}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Done
            </button>
          </div>
        </div>
      )}
    </>
  );
}
