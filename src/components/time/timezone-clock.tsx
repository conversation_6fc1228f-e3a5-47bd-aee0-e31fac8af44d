"use client";

import { useState, useEffect } from "react";
import { formatTime, getShortWeekday, getShortMonth, getWeekNumber } from "@/lib/utils";
import { City, getCityTime } from "@/lib/timezone";

interface TimezoneClockProps {
  city: City;
  showSeconds?: boolean;
  showWeekday?: boolean;
  showDate?: boolean;
  showWeekNumber?: boolean;
  use12Hours?: boolean;
  textColor?: string;
  fontSize?: string;
  fontFamily?: string;
  position?: { x: number; y: number };
  children?: React.ReactNode;
}

export function TimezoneClock({
  city,
  showSeconds = true,
  showWeekday = true,
  showDate = true,
  showWeekNumber = true,
  use12Hours = false,
  textColor = "#000000",
  fontSize = "5rem",
  fontFamily = "monospace",
  position = { x: 0, y: 0 },
  children,
}: TimezoneClockProps) {
  const [time, setTime] = useState<Date | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Set initial time and mounted state
    setTime(getCityTime(city));
    setIsMounted(true);

    const timer = setInterval(() => {
      setTime(getCityTime(city));
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [city]);

  // Update document title with current time
  useEffect(() => {
    if (time && isMounted) {
      const timeString = formatTime(time, showSeconds, use12Hours);
      document.title = `${timeString} - ${city.name} Time`;
    }
  }, [time, showSeconds, use12Hours, isMounted, city.name]);

  // Don't render anything until mounted to avoid hydration mismatch
  if (!isMounted || !time) {
    return (
      <div
        className="flex flex-col items-center justify-center w-full h-full relative"
        style={{
          transform: `translate(${position.x}px, ${position.y}px)`,
          transition: 'transform 0.3s ease',
        }}
      >
        <div
          className="text-center relative"
          style={{
            color: textColor,
            fontSize,
            fontFamily,
          }}
        >
          --:--{showSeconds ? ':--' : ''}
        </div>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col items-center justify-center w-full h-full relative"
      style={{
        transform: `translate(${position.x}px, ${position.y}px)`,
        transition: 'transform 0.3s ease',
      }}
    >
      <div
        className="text-center relative"
        style={{
          color: textColor,
          fontSize,
          fontFamily,
        }}
      >
        {formatTime(time, showSeconds, use12Hours)}
      </div>

      {(showWeekday || showDate || showWeekNumber) && (
        <div
          className="text-center mt-2"
          style={{
            color: textColor,
            fontFamily,
            fontSize: `calc(${fontSize} / 4)`,
          }}
        >
          {(() => {
            const weekday = showWeekday ? getShortWeekday(time) : '';
            const month = showDate ? getShortMonth(time) : '';
            const day = showDate ? time.getDate() : '';
            const year = showDate ? time.getFullYear() : '';
            const weekNumber = showWeekNumber ? getWeekNumber(time) : '';

            let dateString = '';

            if (showWeekday) {
              dateString += weekday;
            }

            if (showDate) {
              if (dateString) dateString += ' - ';
              dateString += `${month} ${day}, ${year}`;
            }

            if (showWeekNumber) {
              if (dateString) dateString += ' - ';
              dateString += `Week ${weekNumber}`;
            }

            return dateString;
          })()}
        </div>
      )}

      {/* City name and timezone info */}
      <div
        className="text-center mt-4"
        style={{
          color: textColor,
          fontFamily,
          fontSize: `calc(${fontSize} / 6)`,
          opacity: 0.8,
        }}
      >
        {city.name}, {city.country}
        <br />
        <span style={{ fontSize: `calc(${fontSize} / 8)` }}>
          {city.timezone}
        </span>
      </div>

      {children}
    </div>
  );
}
