/**
 * Enhanced fullscreen utilities with simulated fullscreen fallback
 */

// Type definitions for browser-specific fullscreen APIs
interface DocumentWithFullscreen extends Document {
  webkitFullscreenElement?: Element;
  mozFullScreenElement?: Element;
  msFullscreenElement?: Element;
  webkitExitFullscreen?: () => Promise<void>;
  mozCancelFullScreen?: () => Promise<void>;
  msExitFullscreen?: () => Promise<void>;
}

interface ElementWithFullscreen extends Element {
  webkitRequestFullscreen?: () => Promise<void>;
  mozRequestFullScreen?: () => Promise<void>;
  msRequestFullscreen?: () => Promise<void>;
}

// Global state for simulated fullscreen
let isSimulatedFullscreen = false;
const simulatedFullscreenCallbacks: (() => void)[] = [];

/**
 * Check if fullscreen is currently active (real or simulated)
 */
export function isFullscreenActive(): boolean {
  const doc = document as DocumentWithFullscreen;
  const realFullscreen = !!(
    doc.fullscreenElement ||
    doc.webkitFullscreenElement ||
    doc.mozFullScreenElement ||
    doc.msFullscreenElement
  );
  return realFullscreen || isSimulatedFullscreen;
}

/**
 * Check if we're in simulated fullscreen mode
 */
export function isSimulatedFullscreenActive(): boolean {
  return isSimulatedFullscreen;
}

/**
 * Check if fullscreen API is supported
 */
export function isFullscreenSupported(): boolean {
  const docElement = document.documentElement as ElementWithFullscreen;
  return !!(
    docElement.requestFullscreen ||
    docElement.webkitRequestFullscreen ||
    docElement.mozRequestFullScreen ||
    docElement.msRequestFullscreen
  );
}

/**
 * Trigger simulated fullscreen callbacks
 */
function triggerSimulatedFullscreenCallbacks(): void {
  simulatedFullscreenCallbacks.forEach(callback => callback());
}

/**
 * Request fullscreen with fallback to simulated fullscreen
 */
export async function requestFullscreen(): Promise<boolean> {
  try {
    const docElement = document.documentElement as ElementWithFullscreen;

    if (docElement.requestFullscreen) {
      await docElement.requestFullscreen();
      return true;
    } else if (docElement.webkitRequestFullscreen) {
      await docElement.webkitRequestFullscreen();
      return true;
    } else if (docElement.mozRequestFullScreen) {
      await docElement.mozRequestFullScreen();
      return true;
    } else if (docElement.msRequestFullscreen) {
      await docElement.msRequestFullscreen();
      return true;
    } else {
      // Fallback to simulated fullscreen
      isSimulatedFullscreen = true;
      triggerSimulatedFullscreenCallbacks();
      return true;
    }
  } catch {
    // If real fullscreen fails, fallback to simulated fullscreen
    isSimulatedFullscreen = true;
    triggerSimulatedFullscreenCallbacks();
    return true;
  }
}

/**
 * Exit fullscreen (real or simulated)
 */
export async function exitFullscreen(): Promise<boolean> {
  try {
    // First check if we're in simulated fullscreen
    if (isSimulatedFullscreen) {
      isSimulatedFullscreen = false;
      triggerSimulatedFullscreenCallbacks();
      return true;
    }

    // Try to exit real fullscreen
    const doc = document as DocumentWithFullscreen;

    if (doc.exitFullscreen) {
      await doc.exitFullscreen();
    } else if (doc.webkitExitFullscreen) {
      await doc.webkitExitFullscreen();
    } else if (doc.mozCancelFullScreen) {
      await doc.mozCancelFullScreen();
    } else if (doc.msExitFullscreen) {
      await doc.msExitFullscreen();
    }
    return true;
  } catch {
    return false;
  }
}

/**
 * Toggle fullscreen state
 */
export async function toggleFullscreen(): Promise<boolean> {
  if (isFullscreenActive()) {
    return await exitFullscreen();
  } else {
    return await requestFullscreen();
  }
}

/**
 * Add fullscreen change event listeners (real and simulated)
 */
export function addFullscreenChangeListener(callback: () => void): () => void {
  const events = [
    'fullscreenchange',
    'webkitfullscreenchange',
    'mozfullscreenchange',
    'MSFullscreenChange'
  ];

  events.forEach(event => {
    document.addEventListener(event, callback);
  });

  // Add to simulated fullscreen callbacks
  simulatedFullscreenCallbacks.push(callback);

  // Return cleanup function
  return () => {
    events.forEach(event => {
      document.removeEventListener(event, callback);
    });

    // Remove from simulated fullscreen callbacks
    const index = simulatedFullscreenCallbacks.indexOf(callback);
    if (index > -1) {
      simulatedFullscreenCallbacks.splice(index, 1);
    }
  };
}


