import citiesData from '@/data/cities.json';

export interface City {
  id: string;
  name: string;
  country: string;
  timezone: string;
  utcOffset: number;
  dstOffset: number;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export function getAllCities(): City[] {
  return citiesData.cities;
}

export function getCityById(id: string): City | null {
  return citiesData.cities.find(city => city.id === id) || null;
}

export function getCityTime(city: City): Date {
  // Create a new date in the city's timezone
  const now = new Date();
  
  // Use Intl.DateTimeFormat to get the time in the specific timezone
  try {
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: city.timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    
    const parts = formatter.formatToParts(now);
    const year = parseInt(parts.find(p => p.type === 'year')?.value || '0');
    const month = parseInt(parts.find(p => p.type === 'month')?.value || '0') - 1; // Month is 0-indexed
    const day = parseInt(parts.find(p => p.type === 'day')?.value || '0');
    const hour = parseInt(parts.find(p => p.type === 'hour')?.value || '0');
    const minute = parseInt(parts.find(p => p.type === 'minute')?.value || '0');
    const second = parseInt(parts.find(p => p.type === 'second')?.value || '0');
    
    return new Date(year, month, day, hour, minute, second);
  } catch (error) {
    console.error('Error getting city time:', error);
    return now;
  }
}

export function getTimeOffset(city: City): { hours: number; minutes: number; isAhead: boolean } {
  const now = new Date();
  const localTime = now;
  const cityTime = getCityTime(city);

  // Calculate the difference in milliseconds
  const diffMs = cityTime.getTime() - localTime.getTime();

  // Convert to hours and minutes
  const totalMinutes = Math.round(diffMs / (1000 * 60));
  const diffHours = Math.floor(Math.abs(totalMinutes) / 60);
  const remainingMinutes = Math.abs(totalMinutes) % 60;

  return {
    hours: diffHours,
    minutes: remainingMinutes,
    isAhead: totalMinutes > 0
  };
}

export function formatTimeOffset(offset: { hours: number; minutes: number; isAhead: boolean }): string {
  const sign = offset.isAhead ? '+' : '-';
  const hoursStr = offset.hours.toString();
  const minutesStr = offset.minutes > 0 ? `:${offset.minutes.toString().padStart(2, '0')}` : '';

  if (offset.hours === 0 && offset.minutes === 0) {
    return 'Same as local time';
  }

  return `${sign}${hoursStr}${minutesStr} hours`;
}

export function formatTimeOffsetShort(offset: { hours: number; minutes: number; isAhead: boolean }): string {
  const sign = offset.isAhead ? '+' : '-';
  const hoursStr = offset.hours.toString();

  if (offset.hours === 0 && offset.minutes === 0) {
    return '0h';
  }

  return `${sign}${hoursStr}h`;
}

export function isDST(): boolean {
  // Simple DST detection - this is a basic implementation
  // In a real application, you might want to use a more sophisticated library
  const now = new Date();
  const jan = new Date(now.getFullYear(), 0, 1);
  const jul = new Date(now.getFullYear(), 6, 1);
  
  const janOffset = jan.getTimezoneOffset();
  const julOffset = jul.getTimezoneOffset();
  
  return Math.max(janOffset, julOffset) !== now.getTimezoneOffset();
}
